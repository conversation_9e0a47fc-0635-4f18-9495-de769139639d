package org.Ver_zhzh.customZombie.UserMaker.gui;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;

import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterConfig;
import org.Ver_zhzh.customZombie.UserMaker.IDZMonsterManager;
import org.Ver_zhzh.customZombie.ParticleHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;

/**
 * 粒子效果编辑器GUI
 * 用于编辑IDZ怪物的粒子特效配置
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ParticleEditorGUI implements Listener {
    
    private final Plugin plugin;
    private final IDZMonsterManager idzManager;
    private final Logger logger;
    private final ParticleHelper particleHelper;
    
    // GUI配置
    private static final String GUI_TITLE = "§6粒子特效编辑器";
    private static final int GUI_SIZE = 63; // 扩大到7行
    private static final int PARTICLES_PER_PAGE = 18; // 每页显示的粒子数量
    private static final int SELECTED_PARTICLES_PER_PAGE = 18; // 已选择特效每页显示数量

    // 玩家编辑状态
    private final Map<Player, String> editingMonster = new HashMap<>();
    private final Map<Player, String> waitingForInput = new HashMap<>();
    private final Map<Player, String> inputType = new HashMap<>();
    private final Map<Player, Set<String>> selectedParticles = new HashMap<>();
    private final Map<Player, BukkitRunnable> previewTasks = new HashMap<>();
    private final Map<Player, String> editingParticleType = new HashMap<>(); // 当前编辑的粒子类型
    private final Map<Player, Integer> currentPage = new HashMap<>(); // 粒子类型选择当前页面
    private final Map<Player, String> searchFilter = new HashMap<>(); // 搜索过滤器
    private final Map<Player, Integer> selectedParticlesPage = new HashMap<>(); // 已选择特效当前页面
    
    // 第1行粒子类型槽位 (0-8)
    private static final int NONE_SLOT = 0;
    private static final int FLAME_SLOT = 1;
    private static final int SMOKE_SLOT = 2;
    private static final int HEART_SLOT = 3;
    private static final int VILLAGER_HAPPY_SLOT = 4;
    private static final int CRIT_SLOT = 5;
    private static final int ENCHANTED_HIT_SLOT = 6;
    private static final int EXPLOSION_SLOT = 7;
    private static final int PORTAL_SLOT = 8;

    // 第2行粒子类型槽位 (9-17)
    private static final int ENCHANT_SLOT = 9;
    private static final int WITCH_SLOT = 10;
    private static final int DRIP_WATER_SLOT = 11;
    private static final int DRIP_LAVA_SLOT = 12;
    private static final int ANGRY_VILLAGER_SLOT = 13;
    private static final int NOTE_SLOT = 14;
    private static final int CLOUD_SLOT = 15;
    private static final int LAVA_SLOT = 16;
    private static final int DUST_SLOT = 17;

    // 第3行分页控制槽位 (18-26)
    private static final int PREV_PAGE_SLOT = 18;
    private static final int PAGE_INFO_SLOT = 22;
    private static final int NEXT_PAGE_SLOT = 26;
    private static final int SEARCH_SLOT = 20;
    private static final int CLEAR_SELECTION_SLOT = 24;

    // 第4-5行已选择特效显示槽位 (27-44)
    private static final int SELECTED_DISPLAY_START = 27;
    private static final int SELECTED_DISPLAY_END = 44; // 扩大到2行，18个槽位

    // 第6行已选择特效分页控制槽位 (45-53)
    private static final int SELECTED_PREV_PAGE_SLOT = 45;
    private static final int SELECTED_PAGE_INFO_SLOT = 46;
    private static final int SELECTED_NEXT_PAGE_SLOT = 47;

    // 第7行主控制按钮槽位 (54-62)
    private static final int PREVIEW_SLOT = 54;
    private static final int STOP_PREVIEW_SLOT = 55;
    private static final int BACK_SLOT = 58;
    private static final int SAVE_SLOT = 62;
    // 注意：移除了 ADVANCED_SETTINGS_SLOT，高级设置按钮将移到粒子参数编辑器中
    
    // 粒子类型数据结构
    private static class ParticleTypeData {
        final String type;
        final String displayName;
        final Material icon;
        final String[] description;
        final String category;

        ParticleTypeData(String type, String displayName, Material icon, String category, String... description) {
            this.type = type;
            this.displayName = displayName;
            this.icon = icon;
            this.description = description;
            this.category = category;
        }
    }

    // 完整的粒子类型列表
    private static final ParticleTypeData[] ALL_PARTICLES = {
        // 基础粒子
        new ParticleTypeData("NONE", "§c无粒子效果", Material.BARRIER, "基础", "§7禁用粒子效果", "§7怪物将不会产生任何粒子"),
        new ParticleTypeData("POOF", "§f烟雾消散", Material.WHITE_WOOL, "基础", "§7产生白色烟雾消散效果", "§7适合传送或消失效果"),
        new ParticleTypeData("EXPLOSION", "§c爆炸粒子", Material.TNT, "基础", "§7产生爆炸粒子效果", "§7震撼的视觉冲击"),
        new ParticleTypeData("EXPLOSION_EMITTER", "§c大型爆炸", Material.TNT_MINECART, "基础", "§7产生大型爆炸发射器效果", "§7更强烈的爆炸视觉"),
        new ParticleTypeData("FIREWORK", "§6烟花粒子", Material.FIREWORK_ROCKET, "基础", "§7产生烟花粒子效果", "§7绚烂的庆祝效果"),

        // 水系粒子
        new ParticleTypeData("BUBBLE", "§b气泡粒子", Material.BUBBLE_CORAL, "水系", "§7产生气泡粒子效果", "§7适合水下场景"),
        new ParticleTypeData("BUBBLE_POP", "§b气泡破裂", Material.BUBBLE_CORAL_BLOCK, "水系", "§7产生气泡破裂效果", "§7清脆的水泡破裂"),
        new ParticleTypeData("BUBBLE_COLUMN_UP", "§b气泡柱", Material.MAGMA_BLOCK, "水系", "§7产生向上的气泡柱", "§7岩浆块的气泡效果"),
        new ParticleTypeData("SPLASH", "§b水花四溅", Material.WATER_BUCKET, "水系", "§7产生水花飞溅效果", "§7清爽的水花"),
        new ParticleTypeData("FISHING", "§b钓鱼粒子", Material.FISHING_ROD, "水系", "§7产生钓鱼时的水花", "§7钓鱼线入水效果"),
        new ParticleTypeData("UNDERWATER", "§b水下粒子", Material.KELP, "水系", "§7产生水下漂浮粒子", "§7水下环境效果"),

        // 魔法粒子
        new ParticleTypeData("CRIT", "§e暴击粒子", Material.GOLDEN_SWORD, "魔法", "§7产生暴击粒子效果", "§7金色的暴击星光"),
        new ParticleTypeData("ENCHANTED_HIT", "§b附魔粒子", Material.ENCHANTED_BOOK, "魔法", "§7产生附魔攻击粒子", "§7蓝色的魔法光芒"),
        new ParticleTypeData("ENCHANT", "§b附魔台粒子", Material.ENCHANTING_TABLE, "魔法", "§7产生附魔台粒子效果", "§7魔法能量环绕"),
        new ParticleTypeData("WITCH", "§5女巫粒子", Material.CAULDRON, "魔法", "§7产生女巫法术粒子", "§7紫色魔法效果"),
        new ParticleTypeData("PORTAL", "§5传送门粒子", Material.OBSIDIAN, "魔法", "§7产生传送门粒子效果", "§7神秘的紫色粒子"),
        new ParticleTypeData("REVERSE_PORTAL", "§5逆向传送门", Material.CRYING_OBSIDIAN, "魔法", "§7产生逆向传送门效果", "§7反向的传送门粒子"),

        // 火系粒子
        new ParticleTypeData("FLAME", "§6火焰粒子", Material.FIRE_CHARGE, "火系", "§7产生火焰粒子效果", "§7适合火系怪物"),
        new ParticleTypeData("SMALL_FLAME", "§6小火焰", Material.TORCH, "火系", "§7产生小型火焰粒子", "§7温和的火焰效果"),
        new ParticleTypeData("SOUL_FIRE_FLAME", "§b灵魂火焰", Material.SOUL_TORCH, "火系", "§7产生蓝色灵魂火焰", "§7神秘的蓝色火焰"),
        new ParticleTypeData("LAVA", "§6岩浆粒子", Material.MAGMA_BLOCK, "火系", "§7产生岩浆粒子效果", "§7炽热的岩浆泡泡"),

        // 烟雾粒子
        new ParticleTypeData("SMOKE", "§7烟雾粒子", Material.COBWEB, "烟雾", "§7产生烟雾粒子效果", "§7灰色的烟雾缭绕"),
        new ParticleTypeData("LARGE_SMOKE", "§7大型烟雾", Material.GRAY_WOOL, "烟雾", "§7产生大型烟雾效果", "§7浓密的烟雾"),
        new ParticleTypeData("WHITE_SMOKE", "§f白色烟雾", Material.WHITE_WOOL, "烟雾", "§7产生白色烟雾效果", "§7纯净的白烟"),
        new ParticleTypeData("CAMPFIRE_COSY_SMOKE", "§7篝火烟雾", Material.CAMPFIRE, "烟雾", "§7产生篝火的温馨烟雾", "§7温暖的篝火烟"),
        new ParticleTypeData("CAMPFIRE_SIGNAL_SMOKE", "§7信号烟雾", Material.HAY_BLOCK, "烟雾", "§7产生信号篝火烟雾", "§7高耸的信号烟"),

        // 自然粒子
        new ParticleTypeData("CLOUD", "§f云朵粒子", Material.WHITE_WOOL, "自然", "§7产生云朵粒子效果", "§7轻盈的白云"),
        new ParticleTypeData("RAIN", "§b雨滴粒子", Material.BLUE_STAINED_GLASS, "自然", "§7产生雨滴粒子效果", "§7清新的雨滴"),
        new ParticleTypeData("SNOWFLAKE", "§f雪花粒子", Material.SNOW, "自然", "§7产生雪花粒子效果", "§7飘洒的雪花"),
        new ParticleTypeData("ASH", "§8灰烬粒子", Material.GRAY_DYE, "自然", "§7产生灰烬粒子效果", "§7飘散的灰烬"),
        new ParticleTypeData("WHITE_ASH", "§f白色灰烬", Material.BONE_MEAL, "自然", "§7产生白色灰烬效果", "§7纯净的白色灰烬"),

        // 情感粒子
        new ParticleTypeData("HEART", "§c爱心粒子", Material.RED_DYE, "情感", "§7产生爱心粒子效果", "§7温馨的红色爱心"),
        new ParticleTypeData("ANGRY_VILLAGER", "§c愤怒粒子", Material.REDSTONE, "情感", "§7产生愤怒村民粒子", "§7红色的愤怒符号"),
        new ParticleTypeData("HAPPY_VILLAGER", "§a开心粒子", Material.EMERALD, "情感", "§7产生开心村民粒子", "§7绿色的开心符号"),

        // 音效粒子
        new ParticleTypeData("NOTE", "§e音符粒子", Material.NOTE_BLOCK, "音效", "§7产生音符粒子效果", "§7彩色的音符符号"),

        // 红石粒子
        new ParticleTypeData("DUST", "§4红石粒子", Material.REDSTONE, "红石", "§7产生红石粒子效果", "§7红色能量粉尘", "§7支持自定义颜色"),
        new ParticleTypeData("DUST_COLOR_TRANSITION", "§4渐变红石", Material.REDSTONE_BLOCK, "红石", "§7产生颜色渐变红石粒子", "§7支持颜色过渡效果"),

        // 滴落粒子
        new ParticleTypeData("DRIPPING_WATER", "§b滴水粒子", Material.WATER_BUCKET, "滴落", "§7产生滴水粒子效果", "§7缓慢滴落的水珠"),
        new ParticleTypeData("DRIPPING_LAVA", "§6滴岩浆粒子", Material.LAVA_BUCKET, "滴落", "§7产生滴岩浆粒子效果", "§7炽热的岩浆滴"),
        new ParticleTypeData("DRIPPING_HONEY", "§6滴蜂蜜粒子", Material.HONEY_BOTTLE, "滴落", "§7产生滴蜂蜜粒子效果", "§7甜蜜的蜂蜜滴"),
        new ParticleTypeData("DRIPPING_OBSIDIAN_TEAR", "§5滴黑曜石泪", Material.CRYING_OBSIDIAN, "滴落", "§7产生黑曜石眼泪滴落", "§7紫色的神秘液滴"),

        // 下落粒子
        new ParticleTypeData("FALLING_WATER", "§b下落水珠", Material.BLUE_ICE, "下落", "§7产生下落水珠效果", "§7快速下落的水珠"),
        new ParticleTypeData("FALLING_LAVA", "§6下落岩浆", Material.MAGMA_CREAM, "下落", "§7产生下落岩浆效果", "§7炽热的岩浆珠"),
        new ParticleTypeData("FALLING_HONEY", "§6下落蜂蜜", Material.HONEYCOMB, "下落", "§7产生下落蜂蜜效果", "§7粘稠的蜂蜜珠"),
        new ParticleTypeData("FALLING_OBSIDIAN_TEAR", "§5下落黑曜石泪", Material.OBSIDIAN, "下落", "§7产生下落黑曜石眼泪", "§7神秘的紫色泪珠"),
        new ParticleTypeData("FALLING_NECTAR", "§e下落花蜜", Material.SWEET_BERRIES, "下落", "§7产生下落花蜜效果", "§7甜美的花蜜珠"),

        // 着陆粒子
        new ParticleTypeData("LANDING_LAVA", "§6岩浆着陆", Material.NETHERRACK, "着陆", "§7产生岩浆着陆效果", "§7岩浆珠着陆溅射"),
        new ParticleTypeData("LANDING_HONEY", "§6蜂蜜着陆", Material.HONEY_BLOCK, "着陆", "§7产生蜂蜜着陆效果", "§7蜂蜜珠着陆溅射"),
        new ParticleTypeData("LANDING_OBSIDIAN_TEAR", "§5黑曜石泪着陆", Material.BLACKSTONE, "着陆", "§7产生黑曜石眼泪着陆", "§7神秘液滴着陆效果"),

        // 下界粒子
        new ParticleTypeData("SOUL", "§b灵魂粒子", Material.SOUL_SAND, "下界", "§7产生灵魂粒子效果", "§7幽蓝的灵魂能量"),
        new ParticleTypeData("CRIMSON_SPORE", "§c绯红孢子", Material.CRIMSON_FUNGUS, "下界", "§7产生绯红森林孢子", "§7红色的森林孢子"),
        new ParticleTypeData("WARPED_SPORE", "§3扭曲孢子", Material.WARPED_FUNGUS, "下界", "§7产生扭曲森林孢子", "§7青色的森林孢子"),

        // 发光粒子
        new ParticleTypeData("GLOW", "§e发光粒子", Material.GLOW_INK_SAC, "发光", "§7产生发光粒子效果", "§7温暖的发光效果"),
        new ParticleTypeData("GLOW_SQUID_INK", "§b发光墨汁", Material.GLOW_ITEM_FRAME, "发光", "§7产生发光鱿鱼墨汁", "§7蓝色发光墨汁"),
        new ParticleTypeData("ELECTRIC_SPARK", "§e电火花", Material.LIGHTNING_ROD, "发光", "§7产生电火花效果", "§7闪烁的电火花"),

        // 特殊效果粒子
        new ParticleTypeData("FLASH", "§f闪光粒子", Material.GLOWSTONE, "特效", "§7产生强烈闪光效果", "§7耀眼的白光闪烁"),
        new ParticleTypeData("SONIC_BOOM", "§8音爆粒子", Material.SCULK_SHRIEKER, "特效", "§7产生音爆冲击波", "§7强力的音波冲击"),
        new ParticleTypeData("TOTEM_OF_UNDYING", "§6不死图腾", Material.TOTEM_OF_UNDYING, "特效", "§7产生不死图腾粒子", "§7金色的复活光芒"),
        new ParticleTypeData("ELDER_GUARDIAN", "§b远古守卫者", Material.PRISMARINE_CRYSTALS, "特效", "§7产生远古守卫者诅咒", "§7蓝色的诅咒光束"),

        // 攻击粒子
        new ParticleTypeData("DAMAGE_INDICATOR", "§c伤害指示", Material.IRON_SWORD, "攻击", "§7产生伤害指示粒子", "§7显示伤害数值"),
        new ParticleTypeData("SWEEP_ATTACK", "§6横扫攻击", Material.DIAMOND_SWORD, "攻击", "§7产生横扫攻击粒子", "§7剑气横扫效果"),

        // 龙息粒子
        new ParticleTypeData("DRAGON_BREATH", "§5龙息粒子", Material.DRAGON_BREATH, "龙系", "§7产生末影龙息效果", "§7紫色的龙息云雾"),
        new ParticleTypeData("END_ROD", "§f末地烛", Material.END_ROD, "龙系", "§7产生末地烛粒子", "§7白色的末地能量"),

        // 药水效果粒子
        new ParticleTypeData("EFFECT", "§d药水效果", Material.POTION, "药水", "§7产生药水效果粒子", "§7彩色的药水粒子"),
        new ParticleTypeData("INSTANT_EFFECT", "§d瞬间效果", Material.SPLASH_POTION, "药水", "§7产生瞬间药水效果", "§7快速的药水粒子"),
        new ParticleTypeData("ENTITY_EFFECT", "§d实体效果", Material.LINGERING_POTION, "药水", "§7产生实体药水效果", "§7环绕实体的药水粒子"),

        // 海洋粒子
        new ParticleTypeData("NAUTILUS", "§b鹦鹉螺粒子", Material.NAUTILUS_SHELL, "海洋", "§7产生鹦鹉螺粒子", "§7蓝色的海洋能量"),
        new ParticleTypeData("DOLPHIN", "§b海豚粒子", Material.COD, "海洋", "§7产生海豚粒子效果", "§7海豚的水花轨迹"),
        new ParticleTypeData("SQUID_INK", "§8鱿鱼墨汁", Material.INK_SAC, "海洋", "§7产生鱿鱼墨汁粒子", "§7黑色的墨汁云"),
        new ParticleTypeData("CURRENT_DOWN", "§b下沉水流", Material.SOUL_SAND, "海洋", "§7产生向下的水流", "§7灵魂沙的下沉气泡"),

        // 方块相关粒子
        new ParticleTypeData("BLOCK", "§8方块粒子", Material.COBBLESTONE, "方块", "§7产生方块破碎粒子", "§7需要指定方块类型"),
        new ParticleTypeData("FALLING_DUST", "§8下落尘土", Material.SAND, "方块", "§7产生下落尘土粒子", "§7需要指定方块类型"),
        new ParticleTypeData("BLOCK_MARKER", "§8方块标记", Material.BARRIER, "方块", "§7产生方块标记粒子", "§7用于标记方块位置"),

        // 物品粒子
        new ParticleTypeData("ITEM", "§e物品粒子", Material.APPLE, "物品", "§7产生物品破碎粒子", "§7需要指定物品类型"),
        new ParticleTypeData("ITEM_SLIME", "§a史莱姆球", Material.SLIME_BALL, "物品", "§7产生史莱姆球粒子", "§7绿色的粘液粒子"),
        new ParticleTypeData("ITEM_SNOWBALL", "§f雪球", Material.SNOWBALL, "物品", "§7产生雪球粒子", "§7白色的雪球碎片"),
        new ParticleTypeData("ITEM_COBWEB", "§f蜘蛛网", Material.COBWEB, "物品", "§7产生蜘蛛网粒子", "§7白色的蛛丝"),

        // 新版植物粒子
        new ParticleTypeData("CHERRY_LEAVES", "§d樱花叶", Material.CHERRY_LEAVES, "植物", "§7产生樱花叶粒子", "§7粉色的樱花花瓣"),
        new ParticleTypeData("PALE_OAK_LEAVES", "§f苍白橡叶", Material.OAK_LEAVES, "植物", "§7产生苍白橡树叶粒子", "§7淡色的橡树叶"),
        new ParticleTypeData("TINTED_LEAVES", "§2着色叶子", Material.JUNGLE_LEAVES, "植物", "§7产生着色叶子粒子", "§7可自定义颜色的叶子"),
        new ParticleTypeData("SPORE_BLOSSOM_AIR", "§d孢子花空气", Material.SPORE_BLOSSOM, "植物", "§7产生孢子花空气粒子", "§7粉色的孢子花粉"),
        new ParticleTypeData("FALLING_SPORE_BLOSSOM", "§d下落孢子花", Material.FLOWERING_AZALEA, "植物", "§7产生下落孢子花粒子", "§7飘落的孢子花粉"),

        // 新版环境粒子
        new ParticleTypeData("DUST_PLUME", "§8尘埃羽流", Material.BROWN_TERRACOTTA, "环境", "§7产生尘埃羽流效果", "§7大范围的尘土飞扬"),
        new ParticleTypeData("GUST", "§f阵风粒子", Material.FEATHER, "环境", "§7产生阵风粒子效果", "§7强烈的风力效果"),
        new ParticleTypeData("SMALL_GUST", "§f小阵风", Material.RABBIT_FOOT, "环境", "§7产生小型阵风效果", "§7温和的风力粒子"),
        new ParticleTypeData("GUST_EMITTER_LARGE", "§f大型风源", Material.WIND_CHARGE, "环境", "§7产生大型风源效果", "§7强力的风源发射器"),
        new ParticleTypeData("GUST_EMITTER_SMALL", "§f小型风源", Material.BREEZE_ROD, "环境", "§7产生小型风源效果", "§7温和的风源发射器"),

        // 新版特殊粒子
        new ParticleTypeData("SNEEZE", "§a喷嚏粒子", Material.GREEN_DYE, "特殊", "§7产生喷嚏粒子效果", "§7绿色的喷嚏粒子"),
        new ParticleTypeData("SPIT", "§f吐痰粒子", Material.SLIME_BALL, "特殊", "§7产生吐痰粒子效果", "§7羊驼的吐痰攻击"),
        new ParticleTypeData("COMPOSTER", "§6堆肥粒子", Material.COMPOSTER, "特殊", "§7产生堆肥粒子效果", "§7堆肥箱的工作粒子"),
        new ParticleTypeData("EGG_CRACK", "§f蛋壳破裂", Material.EGG, "特殊", "§7产生蛋壳破裂粒子", "§7白色的蛋壳碎片"),

        // 雕塑粒子
        new ParticleTypeData("SCULK_SOUL", "§b雕塑灵魂", Material.SCULK, "雕塑", "§7产生雕塑灵魂粒子", "§7蓝色的灵魂能量"),
        new ParticleTypeData("SCULK_CHARGE", "§b雕塑充能", Material.SCULK_CATALYST, "雕塑", "§7产生雕塑充能粒子", "§7雕塑方块的充能效果"),
        new ParticleTypeData("SCULK_CHARGE_POP", "§b雕塑爆发", Material.SCULK_SHRIEKER, "雕塑", "§7产生雕塑充能爆发", "§7充能完成的爆发效果"),
        new ParticleTypeData("SHRIEK", "§8尖啸粒子", Material.ECHO_SHARD, "雕塑", "§7产生尖啸粒子效果", "§7雕塑尖啸器的音波"),
        new ParticleTypeData("VIBRATION", "§5振动粒子", Material.AMETHYST_SHARD, "雕塑", "§7产生振动粒子效果", "§7紫色的振动波纹"),

        // 试炼粒子
        new ParticleTypeData("TRIAL_SPAWNER_DETECTION", "§e试炼检测", Material.TRIAL_SPAWNER, "试炼", "§7产生试炼刷怪笼检测", "§7黄色的检测粒子"),
        new ParticleTypeData("TRIAL_SPAWNER_DETECTION_OMINOUS", "§c不祥检测", Material.OMINOUS_TRIAL_KEY, "试炼", "§7产生不祥试炼检测", "§7红色的不祥检测"),
        new ParticleTypeData("OMINOUS_SPAWNING", "§c不祥生成", Material.OMINOUS_BOTTLE, "试炼", "§7产生不祥生成粒子", "§7邪恶的生成效果"),
        new ParticleTypeData("VAULT_CONNECTION", "§6宝库连接", Material.VAULT, "试炼", "§7产生宝库连接粒子", "§7金色的连接线"),

        // 状态效果粒子
        new ParticleTypeData("RAID_OMEN", "§c袭击预兆", Material.CROSSBOW, "状态", "§7产生袭击预兆粒子", "§7红色的不祥预兆"),
        new ParticleTypeData("TRIAL_OMEN", "§c试炼预兆", Material.HEAVY_CORE, "状态", "§7产生试炼预兆粒子", "§7试炼的不祥征兆"),
        new ParticleTypeData("INFESTED", "§8被感染", Material.SILVERFISH_SPAWN_EGG, "状态", "§7产生被感染粒子", "§7灰色的感染效果"),

        // 其他特殊粒子
        new ParticleTypeData("FIREFLY", "§e萤火虫", Material.GLOW_BERRIES, "特殊", "§7产生萤火虫粒子", "§7闪烁的萤火虫光点"),
        new ParticleTypeData("WAX_ON", "§6上蜡", Material.HONEYCOMB, "特殊", "§7产生上蜡粒子效果", "§7蜂蜡覆盖效果"),
        new ParticleTypeData("WAX_OFF", "§6去蜡", Material.IRON_AXE, "特殊", "§7产生去蜡粒子效果", "§7移除蜂蜡效果"),
        new ParticleTypeData("SCRAPE", "§7刮擦", Material.IRON_HOE, "特殊", "§7产生刮擦粒子效果", "§7刮擦铜块效果"),
        new ParticleTypeData("MYCELIUM", "§5菌丝", Material.MYCELIUM, "特殊", "§7产生菌丝粒子效果", "§7紫色的菌丝孢子"),
    };

    public ParticleEditorGUI(Plugin plugin, IDZMonsterManager idzManager) {
        this.plugin = plugin;
        this.idzManager = idzManager;
        this.logger = plugin.getLogger();
        this.particleHelper = new ParticleHelper(plugin);

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }
    
    /**
     * 打开粒子效果编辑器
     */
    public void openParticleEditor(Player player, String monsterId) {
        openParticleEditor(player, monsterId, true);
    }

    /**
     * 打开粒子效果编辑器
     *
     * @param player 玩家
     * @param monsterId 怪物ID
     * @param resetSelection 是否重置选择状态
     */
    public void openParticleEditor(Player player, String monsterId, boolean resetSelection) {
        try {
            logger.info("=== 开始打开粒子编辑器 ===");
            logger.info("玩家: " + player.getName());
            logger.info("怪物ID: " + monsterId);
            logger.info("重置选择: " + resetSelection);

            IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
            if (config == null) {
                // 尝试重新加载配置
                if (logger != null) {
                    logger.warning("粒子编辑器中找不到怪物配置，尝试重新加载:");
                    logger.warning("- 怪物ID: " + monsterId);
                    logger.warning("- 可用的怪物ID: " + String.join(", ", idzManager.getAllMonsterIds()));
                }

            // 强制重新加载配置
            idzManager.reloadConfig();
            config = idzManager.getMonsterConfig(monsterId);

            if (config == null) {
                player.sendMessage(ChatColor.RED + "❌ IDZ怪物不存在: " + monsterId);
                player.sendMessage(ChatColor.YELLOW + "请检查怪物是否已被删除或配置文件是否损坏");
                return;
            } else {
                if (logger != null) {
                    logger.info("重新加载后成功找到怪物配置: " + monsterId);
                }
            }
        }

        editingMonster.put(player, monsterId);

        // 初始化页面状态
        currentPage.putIfAbsent(player, 0);
        searchFilter.putIfAbsent(player, "");

        // 根据参数决定是否重置选择状态
        if (resetSelection) {
            // 初始化选择的粒子集合
            Set<String> selected = selectedParticles.computeIfAbsent(player, k -> new HashSet<>());
            selected.clear();

            // 从配置中加载粒子类型
            List<String> configParticleTypes = config.getParticleTypes();
            if (configParticleTypes != null && !configParticleTypes.isEmpty()) {
                selected.addAll(configParticleTypes);
            }

            if (logger != null) {
                logger.info("为玩家 " + player.getName() + " 重置粒子选择状态:");
                logger.info("- 怪物ID: " + monsterId);
                logger.info("- 配置中的粒子类型: " + (configParticleTypes != null ? String.join(", ", configParticleTypes) : "无"));
                logger.info("- 加载了 " + selected.size() + " 个粒子类型");
            }
        } else {
            // 确保选择集合存在，但不重置
            selectedParticles.computeIfAbsent(player, k -> new HashSet<>());
        }

        logger.info("开始创建GUI界面...");
        Inventory gui = Bukkit.createInventory(null, GUI_SIZE, GUI_TITLE + " - " + config.getDisplayName());
        logger.info("GUI创建成功，标题: " + GUI_TITLE + " - " + config.getDisplayName());

        // 填充背景
        logger.info("填充背景...");
        fillBackground(gui);

        // 设置粒子类型选择按钮（分页）
        logger.info("设置粒子类型选择按钮...");
        setupParticleTypeButtons(gui, player);

        // 设置分页控制按钮
        logger.info("设置分页控制按钮...");
        setupPaginationButtons(gui, player);

        // 设置当前选择显示
        logger.info("设置当前选择显示...");
        setupSelectedParticlesDisplay(gui, player);

        // 设置已选择特效分页按钮
        logger.info("设置已选择特效分页按钮...");
        setupSelectedParticlesPaginationButtons(gui, player);

        // 设置控制按钮
        logger.info("设置控制按钮...");
        setupControlButtons(gui);

        logger.info("准备打开GUI给玩家...");
        player.openInventory(gui);
        logger.info("GUI已发送给玩家");

        logger.info("为玩家 " + player.getName() + " 打开增强粒子效果编辑器: " + monsterId);

        } catch (Exception e) {
            logger.severe("打开粒子编辑器时发生异常: " + e.getMessage());
            e.printStackTrace();
            player.sendMessage(ChatColor.RED + "打开粒子编辑器时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 设置粒子类型选择按钮（支持分页）
     */
    private void setupParticleTypeButtons(Inventory gui, Player player) {
        Set<String> selected = selectedParticles.getOrDefault(player, new HashSet<>());
        int page = currentPage.getOrDefault(player, 0);
        String filter = searchFilter.getOrDefault(player, "");

        // 获取过滤后的粒子列表
        List<ParticleTypeData> filteredParticles = getFilteredParticles(filter);

        // 计算当前页的粒子
        int startIndex = page * PARTICLES_PER_PAGE;
        int endIndex = Math.min(startIndex + PARTICLES_PER_PAGE, filteredParticles.size());

        // 清空粒子类型区域
        for (int i = 0; i < PARTICLES_PER_PAGE; i++) {
            gui.setItem(i, null);
        }

        // 设置当前页的粒子按钮
        for (int i = startIndex; i < endIndex; i++) {
            ParticleTypeData particleData = filteredParticles.get(i);
            int slot = i - startIndex;

            gui.setItem(slot, createParticleTypeButton(
                particleData.icon,
                particleData.displayName,
                particleData.type,
                selected,
                Arrays.asList(particleData.description)
            ));
        }
    }

    /**
     * 获取过滤后的粒子列表
     */
    private List<ParticleTypeData> getFilteredParticles(String filter) {
        List<ParticleTypeData> filtered = new ArrayList<>();

        for (ParticleTypeData particle : ALL_PARTICLES) {
            if (filter.isEmpty() ||
                particle.displayName.toLowerCase().contains(filter.toLowerCase()) ||
                particle.type.toLowerCase().contains(filter.toLowerCase()) ||
                particle.category.toLowerCase().contains(filter.toLowerCase())) {
                filtered.add(particle);
            }
        }

        return filtered;
    }

    /**
     * 设置分页控制按钮
     */
    private void setupPaginationButtons(Inventory gui, Player player) {
        int page = currentPage.getOrDefault(player, 0);
        String filter = searchFilter.getOrDefault(player, "");
        List<ParticleTypeData> filteredParticles = getFilteredParticles(filter);
        int totalPages = (int) Math.ceil((double) filteredParticles.size() / PARTICLES_PER_PAGE);

        // 上一页按钮
        if (page > 0) {
            ItemStack prevButton = createButton(Material.ARROW,
                "§e← 上一页",
                Arrays.asList("§7点击查看上一页粒子"));
            gui.setItem(PREV_PAGE_SLOT, prevButton);
        } else {
            gui.setItem(PREV_PAGE_SLOT, createButton(Material.GRAY_STAINED_GLASS_PANE, "§7", Arrays.asList()));
        }

        // 页面信息
        ItemStack pageInfo = createButton(Material.BOOK,
            "§6页面信息",
            Arrays.asList(
                "§7当前页: §e" + (page + 1) + "§7/§e" + Math.max(1, totalPages),
                "§7粒子总数: §e" + filteredParticles.size(),
                "§7搜索过滤: §e" + (filter.isEmpty() ? "无" : filter)
            ));
        gui.setItem(PAGE_INFO_SLOT, pageInfo);

        // 下一页按钮
        if (page < totalPages - 1) {
            ItemStack nextButton = createButton(Material.ARROW,
                "§e下一页 →",
                Arrays.asList("§7点击查看下一页粒子"));
            gui.setItem(NEXT_PAGE_SLOT, nextButton);
        } else {
            gui.setItem(NEXT_PAGE_SLOT, createButton(Material.GRAY_STAINED_GLASS_PANE, "§7", Arrays.asList()));
        }

        // 搜索按钮
        ItemStack searchButton = createButton(Material.COMPASS,
            "§b🔍 搜索粒子",
            Arrays.asList(
                "§7点击搜索特定粒子类型",
                "§7当前过滤: §e" + (filter.isEmpty() ? "无" : filter),
                "§e左键: 设置搜索过滤",
                "§c右键: 清除搜索过滤"
            ));
        gui.setItem(SEARCH_SLOT, searchButton);

        // 清空选择按钮
        Set<String> selected = selectedParticles.getOrDefault(player, new HashSet<>());
        ItemStack clearButton = createButton(Material.BARRIER,
            "§c清空所有选择",
            Arrays.asList(
                "§7清空所有已选择的粒子",
                "§7当前选择: §e" + selected.size() + " 个粒子",
                "§c点击清空所有选择"
            ));
        gui.setItem(CLEAR_SELECTION_SLOT, clearButton);
    }
    
    /**
     * 创建粒子类型按钮（支持多选）
     */
    private ItemStack createParticleTypeButton(Material material, String name, String particleType, Set<String> selectedTypes, List<String> description) {
        ItemStack button = new ItemStack(material);
        ItemMeta meta = button.getItemMeta();

        // 创建可变的描述列表副本
        List<String> mutableDescription = new ArrayList<>(description);

        // 检查是否已选择
        boolean isSelected = selectedTypes.contains(particleType);

        if (isSelected) {
            meta.setDisplayName(name + " §a✓");
            mutableDescription.add("");
            mutableDescription.add("§a已选择此粒子类型");
            mutableDescription.add("§e点击取消选择");
            // 添加绿色边框效果
            button.addUnsafeEnchantment(org.bukkit.enchantments.Enchantment.UNBREAKING, 1);
            meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS);
        } else {
            meta.setDisplayName(name);
            mutableDescription.add("");
            if (particleType.equals("NONE")) {
                mutableDescription.add("§e点击清除所有粒子效果");
            } else {
                mutableDescription.add("§e点击添加此粒子类型");
                mutableDescription.add("§7最多可选择5种粒子");
            }
        }

        meta.setLore(mutableDescription);
        button.setItemMeta(meta);
        return button;
    }
    

    
    /**
     * 设置当前选择的粒子显示区域（支持分页）
     */
    private void setupSelectedParticlesDisplay(Inventory gui, Player player) {
        Set<String> selected = selectedParticles.getOrDefault(player, new HashSet<>());

        // 清空显示区域
        for (int i = SELECTED_DISPLAY_START; i <= SELECTED_DISPLAY_END; i++) {
            gui.setItem(i, null);
        }

        if (selected.isEmpty()) {
            // 显示"无选择"提示
            ItemStack noSelection = createButton(Material.GRAY_STAINED_GLASS_PANE,
                "§7无选择的粒子效果",
                Arrays.asList("§7请在上方选择粒子类型"));
            gui.setItem(SELECTED_DISPLAY_START + 4, noSelection); // 居中显示
        } else {
            // 获取分页信息
            int selectedPage = selectedParticlesPage.getOrDefault(player, 0);
            List<String> selectedList = new ArrayList<>(selected);
            int totalPages = (int) Math.ceil((double) selectedList.size() / SELECTED_PARTICLES_PER_PAGE);

            // 计算当前页的显示范围
            int startIndex = selectedPage * SELECTED_PARTICLES_PER_PAGE;
            int endIndex = Math.min(startIndex + SELECTED_PARTICLES_PER_PAGE, selectedList.size());

            // 显示当前页的粒子
            int slot = SELECTED_DISPLAY_START;
            for (int i = startIndex; i < endIndex; i++) {
                String particleType = selectedList.get(i);
                String displayName = getParticleDisplayName(particleType);

                ItemStack selectedParticle = createButton(Material.EMERALD_BLOCK,
                    "§a✓ " + displayName,
                    Arrays.asList(
                        "§7已选择的粒子类型",
                        "§7页面: §e" + (selectedPage + 1) + "§7/§e" + Math.max(1, totalPages),
                        "",
                        "§e左键: 编辑粒子参数",
                        "§c右键: 取消选择此粒子"
                    ));

                gui.setItem(slot, selectedParticle);
                slot++;
            }
        }
    }

    /**
     * 设置已选择特效的分页控制按钮
     */
    private void setupSelectedParticlesPaginationButtons(Inventory gui, Player player) {
        Set<String> selected = selectedParticles.getOrDefault(player, new HashSet<>());
        if (selected.isEmpty()) {
            return; // 没有选择的粒子时不显示分页按钮
        }

        int selectedPage = selectedParticlesPage.getOrDefault(player, 0);
        int totalPages = (int) Math.ceil((double) selected.size() / SELECTED_PARTICLES_PER_PAGE);

        // 上一页按钮
        if (selectedPage > 0) {
            ItemStack prevButton = createButton(Material.ARROW,
                "§e← 上一页",
                Arrays.asList("§7查看上一页已选择的粒子"));
            gui.setItem(SELECTED_PREV_PAGE_SLOT, prevButton);
        } else {
            gui.setItem(SELECTED_PREV_PAGE_SLOT, createButton(Material.GRAY_STAINED_GLASS_PANE, "§7", Arrays.asList()));
        }

        // 页面信息
        ItemStack pageInfo = createButton(Material.BOOK,
            "§6已选择特效页面",
            Arrays.asList(
                "§7当前页: §e" + (selectedPage + 1) + "§7/§e" + Math.max(1, totalPages),
                "§7已选择: §e" + selected.size() + " §7个粒子",
                "§7每页显示: §e" + SELECTED_PARTICLES_PER_PAGE + " §7个"
            ));
        gui.setItem(SELECTED_PAGE_INFO_SLOT, pageInfo);

        // 下一页按钮
        if (selectedPage < totalPages - 1) {
            ItemStack nextButton = createButton(Material.ARROW,
                "§e下一页 →",
                Arrays.asList("§7查看下一页已选择的粒子"));
            gui.setItem(SELECTED_NEXT_PAGE_SLOT, nextButton);
        } else {
            gui.setItem(SELECTED_NEXT_PAGE_SLOT, createButton(Material.GRAY_STAINED_GLASS_PANE, "§7", Arrays.asList()));
        }
    }

    /**
     * 设置控制按钮
     */
    private void setupControlButtons(Inventory gui) {
        // 预览按钮
        ItemStack previewButton = createButton(Material.ENDER_EYE,
            "§a👁 开始预览",
            Arrays.asList(
                "§7预览所有选择的粒子效果",
                "§7使用当前配置参数",
                "§e点击开始预览"
            ));
        gui.setItem(PREVIEW_SLOT, previewButton);

        // 停止预览按钮
        ItemStack stopPreviewButton = createButton(Material.REDSTONE_BLOCK,
            "§c⏹ 停止预览",
            Arrays.asList(
                "§7停止当前的粒子预览",
                "§e点击停止预览"
            ));
        gui.setItem(STOP_PREVIEW_SLOT, stopPreviewButton);



        // 返回按钮
        ItemStack backButton = createButton(Material.ARROW,
            "§e← 返回主菜单",
            Arrays.asList("§7返回IDZ怪物主编辑器"));
        gui.setItem(BACK_SLOT, backButton);

        // 保存按钮
        ItemStack saveButton = createButton(Material.EMERALD,
            "§a✓ 保存配置",
            Arrays.asList("§7保存粒子效果配置"));
        gui.setItem(SAVE_SLOT, saveButton);
    }
    
    /**
     * 创建按钮
     */
    private ItemStack createButton(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 填充背景
     */
    private void fillBackground(Inventory gui) {
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);
        
        // 填充空白区域
        for (int i = 0; i < GUI_SIZE; i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, background);
            }
        }
    }
    
    /**
     * 获取粒子类型的显示材料
     */
    private Material getParticleDisplayMaterial(String particleType) {
        switch (particleType.toUpperCase()) {
            case "FLAME": return Material.FIRE_CHARGE;
            case "SMOKE": return Material.COAL;
            case "HEART": return Material.PINK_DYE;
            case "VILLAGER_HAPPY": return Material.EMERALD;
            case "CRIT": return Material.DIAMOND_SWORD;
            case "ENCHANTED_HIT": return Material.ENCHANTED_BOOK;
            default: return Material.BARRIER;
        }
    }
    
    /**
     * 获取粒子类型的显示名称（支持80+种粒子）
     */
    private String getParticleDisplayName(String particleType) {
        // 从粒子数据中查找显示名称
        for (ParticleTypeData particle : ALL_PARTICLES) {
            if (particle.type.equalsIgnoreCase(particleType)) {
                return particle.displayName;
            }
        }

        // 如果没找到，返回格式化的类型名称
        return "§7" + particleType.toLowerCase().replace("_", " ");
    }

    /**
     * 处理GUI点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();

        if (!title.startsWith(GUI_TITLE) && !title.startsWith("§6粒子参数编辑器") && !title.startsWith("§d高级粒子设置")) {
            return;
        }

        event.setCancelled(true);

        int slot = event.getRawSlot();

        // 处理粒子参数编辑器
        if (title.startsWith("§6粒子参数编辑器")) {
            handleParticleParameterEditorClick(player, slot, event.getClick());
            return;
        }

        // 处理高级粒子设置
        if (title.startsWith("§d高级粒子设置")) {
            handleAdvancedSettingsClick(player, slot, event.getClick());
            return;
        }
        String monsterId = editingMonster.get(player);

        if (monsterId == null) {
            return;
        }

        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            return;
        }

        // 处理粒子类型选择 (第1-2行，槽位0-17)
        if (slot >= 0 && slot <= 17) {
            handleParticleTypeSelection(player, slot, config);
        }
        // 处理分页控制按钮 (第3行，槽位18-26)
        else if (slot == PREV_PAGE_SLOT) {
            handlePreviousPage(player);
        }
        else if (slot == NEXT_PAGE_SLOT) {
            handleNextPage(player);
        }
        else if (slot == SEARCH_SLOT) {
            handleSearchClick(player, event.getClick());
        }
        else if (slot == CLEAR_SELECTION_SLOT) {
            handleClearSelection(player);
        }
        // 处理选择的粒子显示区域点击 (第4-5行，槽位27-44)
        else if (slot >= SELECTED_DISPLAY_START && slot <= SELECTED_DISPLAY_END) {
            handleSelectedParticleClick(player, slot, config, event.getClick());
        }
        // 处理已选择特效分页控制按钮 (第6行，槽位45-47)
        else if (slot == SELECTED_PREV_PAGE_SLOT) {
            handleSelectedParticlesPreviousPage(player);
        }
        else if (slot == SELECTED_NEXT_PAGE_SLOT) {
            handleSelectedParticlesNextPage(player);
        }
        // 处理控制按钮 (第7行，槽位54-62)
        else if (slot == PREVIEW_SLOT) {
            startParticlePreview(player, config);
        }
        else if (slot == STOP_PREVIEW_SLOT) {
            stopParticlePreview(player);
        }

        else if (slot == BACK_SLOT) {
            returnToMainEditor(player, monsterId);
        }
        else if (slot == SAVE_SLOT) {
            saveConfiguration(player, monsterId);
        }
    }

    /**
     * 处理上一页按钮点击
     */
    private void handlePreviousPage(Player player) {
        int currentPageNum = currentPage.getOrDefault(player, 0);
        if (currentPageNum > 0) {
            currentPage.put(player, currentPageNum - 1);
            openParticleEditor(player, editingMonster.get(player), false);
            player.playSound(player.getLocation(), org.bukkit.Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
        }
    }

    /**
     * 处理下一页按钮点击
     */
    private void handleNextPage(Player player) {
        String filter = searchFilter.getOrDefault(player, "");
        List<ParticleTypeData> filteredParticles = getFilteredParticles(filter);
        int totalPages = (int) Math.ceil((double) filteredParticles.size() / PARTICLES_PER_PAGE);
        int currentPageNum = currentPage.getOrDefault(player, 0);

        if (currentPageNum < totalPages - 1) {
            currentPage.put(player, currentPageNum + 1);
            openParticleEditor(player, editingMonster.get(player), false);
            player.playSound(player.getLocation(), org.bukkit.Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
        }
    }

    /**
     * 处理已选择特效上一页按钮点击
     */
    private void handleSelectedParticlesPreviousPage(Player player) {
        int currentSelectedPage = selectedParticlesPage.getOrDefault(player, 0);
        if (currentSelectedPage > 0) {
            selectedParticlesPage.put(player, currentSelectedPage - 1);
            openParticleEditor(player, editingMonster.get(player), false);
            player.playSound(player.getLocation(), org.bukkit.Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
        }
    }

    /**
     * 处理已选择特效下一页按钮点击
     */
    private void handleSelectedParticlesNextPage(Player player) {
        Set<String> selected = selectedParticles.getOrDefault(player, new HashSet<>());
        int totalPages = (int) Math.ceil((double) selected.size() / SELECTED_PARTICLES_PER_PAGE);
        int currentSelectedPage = selectedParticlesPage.getOrDefault(player, 0);

        if (currentSelectedPage < totalPages - 1) {
            selectedParticlesPage.put(player, currentSelectedPage + 1);
            openParticleEditor(player, editingMonster.get(player), false);
            player.playSound(player.getLocation(), org.bukkit.Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
        }
    }

    /**
     * 处理搜索按钮点击
     */
    private void handleSearchClick(Player player, org.bukkit.event.inventory.ClickType clickType) {
        if (clickType == org.bukkit.event.inventory.ClickType.RIGHT) {
            // 右键清除搜索过滤
            searchFilter.put(player, "");
            currentPage.put(player, 0);
            openParticleEditor(player, editingMonster.get(player), false);
            player.sendMessage(ChatColor.GREEN + "✅ 已清除搜索过滤");
            player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);
        } else {
            // 左键设置搜索过滤
            player.closeInventory();
            player.sendMessage("");
            player.sendMessage(ChatColor.GOLD + "========== 粒子搜索 ==========");
            player.sendMessage(ChatColor.YELLOW + "请输入要搜索的粒子关键词:");
            player.sendMessage(ChatColor.GRAY + "支持搜索: 粒子名称、类型、类别");
            player.sendMessage(ChatColor.GRAY + "例如: 火焰、FLAME、魔法、水系等");
            player.sendMessage("");
            player.sendMessage(ChatColor.RED + "输入 'cancel' 取消搜索");
            player.sendMessage(ChatColor.GOLD + "===============================");

            waitingForInput.put(player, editingMonster.get(player));
            inputType.put(player, "search");
        }
    }

    /**
     * 处理清空选择按钮点击
     */
    private void handleClearSelection(Player player) {
        Set<String> selected = selectedParticles.get(player);
        if (selected != null && !selected.isEmpty()) {
            int count = selected.size();
            selected.clear();
            openParticleEditor(player, editingMonster.get(player), false);
            player.sendMessage(ChatColor.YELLOW + "✨ 已清空所有粒子选择 (" + count + "个)");
            player.sendTitle("", ChatColor.YELLOW + "已清空所有粒子选择", 10, 30, 10);
            player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 0.5f);
        } else {
            player.sendMessage(ChatColor.GRAY + "没有需要清空的粒子选择");
        }
    }

    /**
     * 处理粒子类型选择（支持多选）
     */
    private void handleParticleTypeSelection(Player player, int slot, IDZMonsterConfig config) {
        String particleType = getParticleTypeBySlot(player, slot);
        if (particleType == null) return;

        Set<String> selected = selectedParticles.computeIfAbsent(player, k -> new HashSet<>());

        if (particleType.equals("NONE")) {
            // 清除所有选择
            selected.clear();
            player.sendMessage(ChatColor.YELLOW + "✨ 已清除所有粒子效果");
            player.sendTitle("", ChatColor.YELLOW + "已清除所有粒子效果", 10, 30, 10);
            player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 0.5f);
        } else {
            // 切换选择状态
            if (selected.contains(particleType)) {
                selected.remove(particleType);
                player.sendMessage(ChatColor.RED + "➖ 已移除粒子类型: " + ChatColor.BOLD + getParticleDisplayName(particleType));
                player.sendTitle("", ChatColor.RED + "➖ 移除 " + getParticleDisplayName(particleType), 10, 20, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_BASS, 1.0f, 0.8f);
            } else {
                // 移除5个粒子的限制，但添加性能提示
                if (selected.size() >= 10) {
                    player.sendMessage(ChatColor.YELLOW + "⚠ 警告: 选择过多粒子可能影响性能！当前已选择 " + selected.size() + " 个");
                }

                selected.add(particleType);
                player.sendMessage(ChatColor.GREEN + "➕ 已添加粒子类型: " + ChatColor.BOLD + getParticleDisplayName(particleType));
                player.sendTitle("", ChatColor.GREEN + "➕ 添加 " + getParticleDisplayName(particleType), 10, 20, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);

                // 播放简短预览
                playQuickPreview(player, particleType);
            }
        }

        // 重新打开GUI以刷新显示，但不重置选择状态
        openParticleEditor(player, editingMonster.get(player), false);
    }

    /**
     * 根据槽位和当前页面获取粒子类型
     */
    private String getParticleTypeBySlot(Player player, int slot) {
        if (slot < 0 || slot >= PARTICLES_PER_PAGE) return null;

        int page = currentPage.getOrDefault(player, 0);
        String filter = searchFilter.getOrDefault(player, "");
        List<ParticleTypeData> filteredParticles = getFilteredParticles(filter);

        int particleIndex = page * PARTICLES_PER_PAGE + slot;
        if (particleIndex >= filteredParticles.size()) return null;

        return filteredParticles.get(particleIndex).type;
    }

    /**
     * 打开高级设置界面
     */
    private void openAdvancedSettings(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "❌ 配置不存在");
            return;
        }

        editingMonster.put(player, monsterId);

        Inventory gui = Bukkit.createInventory(null, 54, "§d高级粒子设置 - " + config.getDisplayName());

        // 填充背景
        fillBackground(gui);

        // 设置高级参数按钮
        setupAdvancedParameterButtons(gui, config);

        // 设置控制按钮
        setupAdvancedControlButtons(gui);

        player.openInventory(gui);

        if (logger != null) {
            logger.info("为玩家 " + player.getName() + " 打开高级粒子设置: " + monsterId);
        }
    }

    /**
     * 设置高级参数按钮
     */
    private void setupAdvancedParameterButtons(Inventory gui, IDZMonsterConfig config) {
        // 基础参数区域 (第2行)
        ItemStack countButton = createParameterEditButton(
            Material.EMERALD,
            "§a粒子数量",
            config.getParticleCount(),
            "个",
            Arrays.asList(
                "§7控制每次生成的粒子数量",
                "§7数量越多效果越密集",
                "§7范围: 1-100"
            )
        );
        gui.setItem(10, countButton);

        ItemStack rangeButton = createParameterEditButton(
            Material.COMPASS,
            "§b粒子范围",
            config.getParticleRange(),
            "格",
            Arrays.asList(
                "§7控制粒子散布的范围",
                "§7范围越大粒子分布越广",
                "§7范围: 0.5-10.0"
            )
        );
        gui.setItem(11, rangeButton);

        ItemStack intervalButton = createParameterEditButton(
            Material.CLOCK,
            "§e粒子间隔",
            config.getParticleInterval(),
            "tick",
            Arrays.asList(
                "§7控制粒子生成的时间间隔",
                "§7间隔越小生成越频繁",
                "§7范围: 5-100 (20tick=1秒)"
            )
        );
        gui.setItem(12, intervalButton);

        ItemStack durationButton = createParameterEditButton(
            Material.REPEATER,
            "§d预览时长",
            config.getPreviewDuration(),
            "秒",
            Arrays.asList(
                "§7控制预览粒子的持续时间",
                "§7时长越长预览越久",
                "§7范围: 1-30"
            )
        );
        gui.setItem(13, durationButton);

        // 高级参数区域 (第3行) - 这些是新增的参数
        ItemStack speedButton = createParameterEditButton(
            Material.FEATHER,
            "§f粒子速度",
            0.1, // 默认速度
            "",
            Arrays.asList(
                "§7控制粒子移动速度",
                "§7速度越高粒子移动越快",
                "§7范围: 0.0-2.0"
            )
        );
        gui.setItem(19, speedButton);

        ItemStack offsetXButton = createParameterEditButton(
            Material.REDSTONE,
            "§cX轴偏移",
            0.5, // 默认偏移
            "格",
            Arrays.asList(
                "§7控制粒子在X轴的随机偏移",
                "§7偏移越大粒子分布越散",
                "§7范围: 0.0-5.0"
            )
        );
        gui.setItem(20, offsetXButton);

        ItemStack offsetYButton = createParameterEditButton(
            Material.LIME_DYE,
            "§aY轴偏移",
            0.5, // 默认偏移
            "格",
            Arrays.asList(
                "§7控制粒子在Y轴的随机偏移",
                "§7偏移越大粒子分布越散",
                "§7范围: 0.0-5.0"
            )
        );
        gui.setItem(21, offsetYButton);

        ItemStack offsetZButton = createParameterEditButton(
            Material.BLUE_DYE,
            "§bZ轴偏移",
            0.5, // 默认偏移
            "格",
            Arrays.asList(
                "§7控制粒子在Z轴的随机偏移",
                "§7偏移越大粒子分布越散",
                "§7范围: 0.0-5.0"
            )
        );
        gui.setItem(22, offsetZButton);

        // 颜色和特殊参数区域 (第4行)
        ItemStack colorButton = createParameterEditButton(
            Material.RED_DYE,
            "§c粒子颜色",
            "默认",
            "",
            Arrays.asList(
                "§7设置粒子颜色 (适用于DUST等)",
                "§7支持RGB颜色代码",
                "§7格式: #RRGGBB 或 默认"
            )
        );
        gui.setItem(28, colorButton);

        ItemStack sizeButton = createParameterEditButton(
            Material.SLIME_BALL,
            "§a粒子大小",
            1.0, // 默认大小
            "",
            Arrays.asList(
                "§7设置粒子大小 (适用于DUST等)",
                "§7大小越大粒子越明显",
                "§7范围: 0.1-5.0"
            )
        );
        gui.setItem(29, sizeButton);
    }

    /**
     * 设置高级设置的控制按钮
     */
    private void setupAdvancedControlButtons(Inventory gui) {
        // 返回按钮
        ItemStack backButton = createButton(Material.ARROW,
            "§e← 返回粒子编辑器",
            Arrays.asList("§7返回粒子效果编辑界面"));
        gui.setItem(45, backButton);

        // 重置为默认值按钮
        ItemStack resetButton = createButton(Material.BARRIER,
            "§c重置默认值",
            Arrays.asList(
                "§7将所有高级参数重置为默认值",
                "§c警告: 此操作不可撤销"
            ));
        gui.setItem(46, resetButton);

        // 预设管理按钮
        ItemStack presetButton = createButton(Material.BOOK,
            "§6📋 预设管理",
            Arrays.asList(
                "§7保存或加载粒子参数预设",
                "§7快速应用常用配置",
                "§e点击打开预设管理"
            ));
        gui.setItem(48, presetButton);

        // 预览按钮
        ItemStack previewButton = createButton(Material.ENDER_EYE,
            "§a👁 预览效果",
            Arrays.asList("§7使用当前高级参数预览粒子"));
        gui.setItem(52, previewButton);

        // 保存按钮
        ItemStack saveButton = createButton(Material.EMERALD,
            "§a✓ 保存设置",
            Arrays.asList("§7保存所有高级参数设置"));
        gui.setItem(53, saveButton);
    }

    /**
     * 处理高级设置界面的点击事件
     */
    private void handleAdvancedSettingsClick(Player player, int slot, org.bukkit.event.inventory.ClickType clickType) {
        String monsterId = editingMonster.get(player);
        if (monsterId == null) return;

        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) return;

        // 基础参数
        if (slot == 10) {
            requestParameterInput(player, "count", "粒子数量", config.getParticleCount(), "个", "1-100");
        } else if (slot == 11) {
            requestParameterInput(player, "range", "粒子范围", config.getParticleRange(), "格", "0.5-10.0");
        } else if (slot == 12) {
            requestParameterInput(player, "interval", "粒子间隔", config.getParticleInterval(), "tick", "5-100");
        } else if (slot == 13) {
            requestParameterInput(player, "duration", "预览时长", config.getPreviewDuration(), "秒", "1-30");
        }
        // 高级参数 (新增)
        else if (slot == 19) {
            requestParameterInput(player, "speed", "粒子速度", 0.1, "", "0.0-2.0");
        } else if (slot == 20) {
            requestParameterInput(player, "offsetX", "X轴偏移", 0.5, "格", "0.0-5.0");
        } else if (slot == 21) {
            requestParameterInput(player, "offsetY", "Y轴偏移", 0.5, "格", "0.0-5.0");
        } else if (slot == 22) {
            requestParameterInput(player, "offsetZ", "Z轴偏移", 0.5, "格", "0.0-5.0");
        } else if (slot == 28) {
            requestParameterInput(player, "color", "粒子颜色", "默认", "", "#RRGGBB 或 默认");
        } else if (slot == 29) {
            requestParameterInput(player, "size", "粒子大小", 1.0, "", "0.1-5.0");
        }
        // 控制按钮
        else if (slot == 45) {
            // 返回粒子编辑器
            openParticleEditor(player, monsterId, false);
        } else if (slot == 46) {
            // 重置为默认值
            handleResetToDefaults(player, monsterId);
        } else if (slot == 48) {
            // 预设管理
            handlePresetManagement(player, monsterId);
        } else if (slot == 52) {
            // 预览效果
            handleAdvancedPreview(player, config);
        } else if (slot == 53) {
            // 保存设置
            handleSaveAdvancedSettings(player, monsterId);
        }
    }

    /**
     * 处理重置为默认值
     */
    private void handleResetToDefaults(Player player, String monsterId) {
        player.sendMessage(ChatColor.YELLOW + "⚠ 确认要重置所有高级参数为默认值吗？");
        player.sendMessage(ChatColor.GRAY + "输入 'yes' 确认，或 'no' 取消");

        waitingForInput.put(player, monsterId);
        inputType.put(player, "reset_confirm");
        player.closeInventory();
    }

    /**
     * 处理预设管理
     */
    private void handlePresetManagement(Player player, String monsterId) {
        player.sendMessage(ChatColor.GOLD + "📋 粒子预设管理");
        player.sendMessage(ChatColor.YELLOW + "功能开发中，敬请期待！");
        player.sendMessage(ChatColor.GRAY + "将支持保存和加载常用的粒子配置预设");
    }

    /**
     * 处理高级预览
     */
    private void handleAdvancedPreview(Player player, IDZMonsterConfig config) {
        Set<String> selected = selectedParticles.get(player);
        if (selected == null || selected.isEmpty()) {
            player.sendMessage(ChatColor.YELLOW + "请先选择要预览的粒子效果");
            return;
        }

        player.sendMessage(ChatColor.GREEN + "🎆 开始高级粒子预览...");
        player.sendMessage(ChatColor.GRAY + "使用当前的高级参数设置");

        // 这里可以添加使用高级参数的预览逻辑
        startParticlePreview(player, config);
    }

    /**
     * 处理保存高级设置
     */
    private void handleSaveAdvancedSettings(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "❌ 配置不存在，无法保存");
            return;
        }

        try {
            // 保存配置
            boolean saveResult = idzManager.updateMonsterConfig(monsterId, config);

            if (saveResult) {
                player.sendMessage(ChatColor.GREEN + "✅ 高级粒子设置已成功保存！");
                player.sendTitle("", ChatColor.GREEN + "✅ 高级设置保存成功", 10, 30, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);

                if (logger != null) {
                    logger.info("高级粒子设置保存成功: " + monsterId);
                }
            } else {
                player.sendMessage(ChatColor.RED + "❌ 保存高级设置失败，请检查控制台错误信息");
                player.sendTitle("", ChatColor.RED + "❌ 保存失败", 10, 30, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);

                if (logger != null) {
                    logger.warning("高级粒子设置保存失败: " + monsterId);
                }
            }
        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "❌ 保存时发生错误: " + e.getMessage());
            if (logger != null) {
                logger.severe("保存高级粒子设置时发生异常: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 根据槽位获取粒子类型
     */
    private String getParticleTypeBySlot(int slot) {
        switch (slot) {
            case NONE_SLOT: return "NONE";
            case FLAME_SLOT: return "FLAME";
            case SMOKE_SLOT: return "SMOKE";
            case HEART_SLOT: return "HEART";
            case VILLAGER_HAPPY_SLOT: return "VILLAGER_HAPPY";
            case CRIT_SLOT: return "CRIT";
            case ENCHANTED_HIT_SLOT: return "ENCHANTED_HIT";
            case EXPLOSION_SLOT: return "EXPLOSION_NORMAL";
            case PORTAL_SLOT: return "PORTAL";
            case ENCHANT_SLOT: return "ENCHANTMENT_TABLE";
            case WITCH_SLOT: return "SPELL_WITCH";
            case DRIP_WATER_SLOT: return "DRIP_WATER";
            case DRIP_LAVA_SLOT: return "DRIP_LAVA";
            case ANGRY_VILLAGER_SLOT: return "VILLAGER_ANGRY";
            case NOTE_SLOT: return "NOTE";
            case CLOUD_SLOT: return "CLOUD";
            case LAVA_SLOT: return "LAVA";
            case DUST_SLOT: return "REDSTONE";
            default: return null;
        }
    }





    /**
     * 处理选择的粒子显示区域点击（支持分页）
     */
    private void handleSelectedParticleClick(Player player, int slot, IDZMonsterConfig config, org.bukkit.event.inventory.ClickType clickType) {
        Set<String> selected = selectedParticles.get(player);
        if (selected == null || selected.isEmpty()) return;

        // 计算点击的是当前页第几个粒子
        int slotIndex = slot - SELECTED_DISPLAY_START;
        if (slotIndex < 0 || slotIndex >= SELECTED_PARTICLES_PER_PAGE) return;

        // 获取分页信息
        int selectedPage = selectedParticlesPage.getOrDefault(player, 0);
        int actualIndex = selectedPage * SELECTED_PARTICLES_PER_PAGE + slotIndex;

        // 将Set转换为List以便按索引访问
        List<String> selectedList = new ArrayList<>(selected);
        if (actualIndex < selectedList.size()) {
            String particleType = selectedList.get(actualIndex);

            if (clickType == org.bukkit.event.inventory.ClickType.RIGHT) {
                // 右键删除粒子
                selected.remove(particleType);
                player.sendMessage(ChatColor.RED + "➖ 已移除粒子类型: " + ChatColor.BOLD + getParticleDisplayName(particleType));
                player.sendTitle("", ChatColor.RED + "➖ 移除 " + getParticleDisplayName(particleType), 10, 20, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_BASS, 1.0f, 0.8f);

                // 检查当前页是否还有粒子，如果没有则回到上一页
                int totalPages = (int) Math.ceil((double) selected.size() / SELECTED_PARTICLES_PER_PAGE);
                if (selectedPage >= totalPages && selectedPage > 0) {
                    selectedParticlesPage.put(player, selectedPage - 1);
                }

                // 重新打开GUI以刷新显示，但不重置选择状态
                openParticleEditor(player, editingMonster.get(player), false);
            } else if (clickType == org.bukkit.event.inventory.ClickType.LEFT) {
                // 左键编辑粒子参数
                openParticleParameterEditor(player, editingMonster.get(player), particleType);
            }
        }
    }

    /**
     * 播放快速预览（0.5秒）
     */
    private void playQuickPreview(Player player, String particleType) {
        Particle bukkitParticle = getBukkitParticle(particleType);
        if (bukkitParticle == null) return;

        try {
            org.bukkit.Location particleLocation = player.getLocation().clone().add(0, 1, 0);

            // 直接使用Bukkit的粒子生成方法
            if (bukkitParticle == Particle.DUST) {
                // 红石粒子需要特殊处理
                org.bukkit.Particle.DustOptions dustOptions = new org.bukkit.Particle.DustOptions(
                    org.bukkit.Color.RED, 1.0f);
                player.getWorld().spawnParticle(bukkitParticle, particleLocation, 5, 0.3, 0.3, 0.3, 0.1, dustOptions);
            } else {
                // 普通粒子
                player.getWorld().spawnParticle(bukkitParticle, particleLocation, 5, 0.3, 0.3, 0.3, 0.1);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("快速预览粒子生成失败: " + particleType + " - " + e.getMessage());
        }
    }

    /**
     * 请求聊天输入
     */
    private void requestInput(Player player, String type, String prompt) {
        waitingForInput.put(player, editingMonster.get(player));
        inputType.put(player, type);

        player.closeInventory();
        player.sendMessage(ChatColor.YELLOW + prompt);
        player.sendMessage(ChatColor.GRAY + "输入 'cancel' 取消修改");
    }

    /**
     * 处理聊天输入
     */
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();

        if (!waitingForInput.containsKey(player)) {
            return;
        }

        event.setCancelled(true);

        String input = event.getMessage().trim();
        String type = inputType.get(player);
        String monsterId = waitingForInput.get(player);

        // 清理输入状态
        waitingForInput.remove(player);
        inputType.remove(player);

        if ("cancel".equalsIgnoreCase(input)) {
            player.sendMessage(ChatColor.GRAY + "已取消修改");
            // 根据当前编辑状态决定返回哪个GUI
            String particleType = editingParticleType.get(player);
            if (particleType != null) {
                // 返回粒子参数编辑器
                Bukkit.getScheduler().runTask(plugin, () -> openParticleParameterEditor(player, monsterId, particleType));
            } else {
                // 返回粒子编辑器
                Bukkit.getScheduler().runTask(plugin, () -> openParticleEditor(player, monsterId, false));
            }
            return;
        }

        // 处理搜索输入
        if (type.equals("search")) {
            processSearchInput(player, input, monsterId);
            return;
        }

        // 处理参数输入
        processParameterInput(player, type, input, monsterId);
    }

    /**
     * 处理搜索输入
     */
    private void processSearchInput(Player player, String input, String monsterId) {
        // 清理输入状态
        waitingForInput.remove(player);
        inputType.remove(player);

        if (input.trim().isEmpty()) {
            player.sendMessage(ChatColor.RED + "搜索关键词不能为空！");
            Bukkit.getScheduler().runTask(plugin, () -> openParticleEditor(player, monsterId, false));
            return;
        }

        // 设置搜索过滤器
        searchFilter.put(player, input.trim());
        currentPage.put(player, 0); // 重置到第一页

        // 获取搜索结果数量
        List<ParticleTypeData> filteredParticles = getFilteredParticles(input.trim());

        player.sendMessage(ChatColor.GREEN + "🔍 搜索完成！");
        player.sendMessage(ChatColor.YELLOW + "关键词: §e" + input.trim());
        player.sendMessage(ChatColor.YELLOW + "找到 §e" + filteredParticles.size() + " §7个匹配的粒子类型");

        if (filteredParticles.isEmpty()) {
            player.sendMessage(ChatColor.GRAY + "提示: 尝试使用更简单的关键词，如 '火'、'水'、'魔法' 等");
        }

        // 重新打开GUI显示搜索结果
        Bukkit.getScheduler().runTask(plugin, () -> openParticleEditor(player, monsterId, false));
    }

    /**
     * 处理参数输入
     */
    private void processParameterInput(Player player, String type, String input, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            // 添加详细的调试信息
            if (logger != null) {
                logger.warning("粒子参数编辑时找不到怪物配置:");
                logger.warning("- 玩家: " + player.getName());
                logger.warning("- 怪物ID: " + monsterId);
                logger.warning("- 参数类型: " + type);
                logger.warning("- 输入值: " + input);
                logger.warning("- 可用的怪物ID: " + String.join(", ", idzManager.getAllMonsterIds()));
            }
            player.sendMessage(ChatColor.RED + "❌ 配置不存在，怪物ID: " + monsterId);
            player.sendMessage(ChatColor.YELLOW + "请尝试重新打开粒子编辑器");
            return;
        }

        try {
            switch (type) {
                case "count":
                    int count = Integer.parseInt(input);
                    if (count < 1 || count > 100) {
                        player.sendMessage(ChatColor.RED + "粒子数量必须在1-100之间！");
                    } else {
                        config.setParticleCount(count);
                        player.sendMessage(ChatColor.GREEN + "粒子数量已设置为: " + count);
                    }
                    break;

                case "range":
                    double range = Double.parseDouble(input);
                    if (range < 1.0 || range > 10.0) {
                        player.sendMessage(ChatColor.RED + "粒子范围必须在1.0-10.0之间！");
                    } else {
                        config.setParticleRange(range);
                        player.sendMessage(ChatColor.GREEN + "粒子范围已设置为: " + range);
                    }
                    break;

                case "interval":
                    int interval = Integer.parseInt(input);
                    if (interval < 5 || interval > 100) {
                        player.sendMessage(ChatColor.RED + "粒子间隔必须在5-100 tick之间！");
                    } else {
                        config.setParticleInterval(interval);
                        player.sendMessage(ChatColor.GREEN + "粒子间隔已设置为: " + interval + " tick");
                    }
                    break;

                case "duration":
                    int duration = Integer.parseInt(input);
                    if (duration < 1 || duration > 30) {
                        player.sendMessage(ChatColor.RED + "预览时长必须在1-30秒之间！");
                    } else {
                        config.setPreviewDuration(duration);
                        player.sendMessage(ChatColor.GREEN + "预览时长已设置为: " + duration + " 秒");
                    }
                    break;
            }
        } catch (NumberFormatException e) {
            player.sendMessage(ChatColor.RED + "输入格式错误！请输入有效的数值。");
        }

        // 根据当前编辑状态决定返回哪个GUI
        String particleType = editingParticleType.get(player);
        if (particleType != null) {
            // 返回粒子参数编辑器
            Bukkit.getScheduler().runTask(plugin, () -> openParticleParameterEditor(player, monsterId, particleType));
        } else {
            // 返回粒子编辑器
            Bukkit.getScheduler().runTask(plugin, () -> openParticleEditor(player, monsterId, false));
        }
    }

    /**
     * 开始粒子预览（支持多粒子和自定义时间）
     */
    private void startParticlePreview(Player player, IDZMonsterConfig config) {
        Set<String> selected = selectedParticles.get(player);
        if (selected == null || selected.isEmpty()) {
            player.sendMessage(ChatColor.YELLOW + "请先选择要预览的粒子效果");
            return;
        }

        // 停止之前的预览
        stopParticlePreview(player);

        int previewDuration = config.getPreviewDuration();
        player.sendMessage(ChatColor.GREEN + "🎬 开始预览 " + selected.size() + " 种粒子效果");
        player.sendMessage(ChatColor.GRAY + "⏱️ 预览时间: " + previewDuration + " 秒");
        player.sendTitle("", ChatColor.GREEN + "🎬 开始预览粒子效果", 10, 30, 10);
        player.playSound(player.getLocation(), org.bukkit.Sound.BLOCK_NOTE_BLOCK_CHIME, 1.0f, 1.5f);

        // 创建预览任务
        BukkitRunnable previewTask = new BukkitRunnable() {
            int ticks = 0;
            final int maxTicks = previewDuration * 20; // 转换为tick

            @Override
            public void run() {
                if (ticks >= maxTicks || !player.isOnline()) {
                    if (player.isOnline()) {
                        player.sendMessage(ChatColor.GREEN + "粒子效果预览结束");
                    }
                    previewTasks.remove(player);
                    this.cancel();
                    return;
                }

                // 为每种选择的粒子生成效果
                for (String particleType : selected) {
                    Particle bukkitParticle = getBukkitParticle(particleType);
                    if (bukkitParticle != null) {
                        try {
                            // 在玩家周围不同位置生成粒子，避免重叠
                            double angle = Math.random() * Math.PI * 2;
                            double offsetX = Math.cos(angle) * 0.5;
                            double offsetZ = Math.sin(angle) * 0.5;

                            org.bukkit.Location particleLocation = player.getLocation().clone().add(offsetX, 1, offsetZ);

                            // 直接使用Bukkit的粒子生成方法
                            if (bukkitParticle == Particle.DUST) {
                                // 红石粒子需要特殊处理
                                org.bukkit.Particle.DustOptions dustOptions = new org.bukkit.Particle.DustOptions(
                                    org.bukkit.Color.RED, 1.0f);
                                player.getWorld().spawnParticle(bukkitParticle, particleLocation,
                                    config.getParticleCount() / selected.size(),
                                    config.getParticleRange() * 0.2,
                                    config.getParticleRange() * 0.2,
                                    config.getParticleRange() * 0.2,
                                    0.1, dustOptions);
                            } else {
                                // 普通粒子
                                player.getWorld().spawnParticle(bukkitParticle, particleLocation,
                                    config.getParticleCount() / selected.size(),
                                    config.getParticleRange() * 0.2,
                                    config.getParticleRange() * 0.2,
                                    config.getParticleRange() * 0.2,
                                    0.1);
                            }
                        } catch (Exception e) {
                            // 如果粒子生成失败，记录错误但继续其他粒子
                            plugin.getLogger().warning("粒子生成失败: " + particleType + " - " + e.getMessage());
                        }
                    }
                }

                ticks += config.getParticleInterval();

                // 显示剩余时间（每秒更新一次）
                if (ticks % 20 == 0) {
                    int remainingSeconds = (maxTicks - ticks) / 20;
                    player.sendActionBar(ChatColor.YELLOW + "预览剩余时间: " + remainingSeconds + "秒");
                }
            }
        };

        previewTask.runTaskTimer(plugin, 0, config.getParticleInterval());
        previewTasks.put(player, previewTask);
    }

    /**
     * 停止粒子预览
     */
    private void stopParticlePreview(Player player) {
        BukkitRunnable task = previewTasks.remove(player);
        if (task != null && !task.isCancelled()) {
            task.cancel();
            player.sendMessage(ChatColor.YELLOW + "已停止粒子预览");
        }
    }

    /**
     * 获取对应的Bukkit粒子类型（支持18种粒子）
     */
    private Particle getBukkitParticle(String particleType) {
        switch (particleType.toUpperCase()) {
            case "FLAME": return Particle.FLAME;
            case "SMOKE": return Particle.SMOKE;
            case "HEART": return Particle.HEART;
            case "VILLAGER_HAPPY": return Particle.HAPPY_VILLAGER;
            case "CRIT": return Particle.CRIT;
            case "ENCHANTED_HIT": return Particle.ENCHANTED_HIT;
            case "EXPLOSION_NORMAL": return Particle.POOF;
            case "PORTAL": return Particle.PORTAL;
            case "ENCHANTMENT_TABLE": return Particle.ENCHANT;
            case "SPELL_WITCH": return Particle.WITCH;
            case "DRIP_WATER": return Particle.DRIPPING_WATER;
            case "DRIP_LAVA": return Particle.DRIPPING_LAVA;
            case "VILLAGER_ANGRY": return Particle.ANGRY_VILLAGER;
            case "NOTE": return Particle.NOTE;
            case "CLOUD": return Particle.CLOUD;
            case "LAVA": return Particle.LAVA;
            case "REDSTONE": return Particle.DUST;
            default: return null;
        }
    }

    /**
     * 返回主编辑器
     */
    private void returnToMainEditor(Player player, String monsterId) {
        // 清理玩家状态
        editingMonster.remove(player);
        waitingForInput.remove(player);
        inputType.remove(player);

        player.closeInventory();

        // 重新打开主编辑器
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            idzManager.getGuiManager().openMainEditor(player, monsterId);
        }, 1L);
    }

    /**
     * 保存配置（支持多粒子）
     */
    private void saveConfiguration(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "❌ 配置不存在，无法保存");
            logger.warning("尝试保存不存在的怪物配置: " + monsterId);
            return;
        }

        // 获取选择的粒子并保存
        Set<String> selected = selectedParticles.get(player);

        // 调试日志
        if (logger != null) {
            logger.info("玩家 " + player.getName() + " 尝试保存粒子配置:");
            logger.info("- 怪物ID: " + monsterId);
            logger.info("- 选择的粒子数量: " + (selected != null ? selected.size() : 0));
            if (selected != null && !selected.isEmpty()) {
                logger.info("- 选择的粒子类型: " + String.join(", ", selected));
            }
        }

        try {
            // 设置粒子类型到配置
            if (selected != null) {
                config.setParticleTypes(new ArrayList<>(selected));
            } else {
                config.setParticleTypes(new ArrayList<>());
            }

            // 保存配置
            boolean saveResult = idzManager.updateMonsterConfig(monsterId, config);

            if (saveResult) {
                player.sendMessage(ChatColor.GREEN + "✅ 粒子效果配置已成功保存！");
                player.sendTitle("", ChatColor.GREEN + "✅ 配置保存成功", 10, 30, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);

                player.sendMessage(ChatColor.GRAY + "配置详情:");
                if (selected == null || selected.isEmpty()) {
                    player.sendMessage(ChatColor.GRAY + "- 粒子类型: " + ChatColor.AQUA + "无粒子");
                } else {
                    player.sendMessage(ChatColor.GRAY + "- 粒子类型: " + ChatColor.AQUA + selected.size() + " 种");
                    for (String particleType : selected) {
                        player.sendMessage(ChatColor.GRAY + "  • " + ChatColor.YELLOW + getParticleDisplayName(particleType));
                    }
                }

                if (logger != null) {
                    logger.info("粒子配置保存成功: " + monsterId);
                }
            } else {
                player.sendMessage(ChatColor.RED + "❌ 保存配置失败，请检查控制台错误信息");
                player.sendTitle("", ChatColor.RED + "❌ 保存失败", 10, 30, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);

                if (logger != null) {
                    logger.warning("粒子配置保存失败: " + monsterId);
                }
            }

        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "❌ 保存配置时发生错误: " + e.getMessage());
            player.sendTitle("", ChatColor.RED + "❌ 保存出错", 10, 30, 10);
            player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);

            if (logger != null) {
                logger.severe("保存粒子配置时发生异常: " + monsterId);
                e.printStackTrace();
            }
        }

        player.sendMessage(ChatColor.GRAY + "- 粒子数量: " + ChatColor.AQUA + config.getParticleCount() + " 个");
        player.sendMessage(ChatColor.GRAY + "- 粒子范围: " + ChatColor.AQUA + config.getParticleRange() + " 格");
        player.sendMessage(ChatColor.GRAY + "- 粒子间隔: " + ChatColor.AQUA + config.getParticleInterval() + " tick");
        player.sendMessage(ChatColor.GRAY + "- 预览时间: " + ChatColor.AQUA + config.getPreviewDuration() + " 秒");

        // 返回主编辑器
        returnToMainEditor(player, monsterId);
    }

    /**
     * 清理玩家数据
     */
    public void cleanupPlayer(Player player) {
        editingMonster.remove(player);
        waitingForInput.remove(player);
        inputType.remove(player);
        selectedParticles.remove(player);

        // 停止预览任务
        stopParticlePreview(player);

        // 清理粒子参数编辑状态
        editingParticleType.remove(player);
    }

    /**
     * 打开粒子参数编辑器
     */
    private void openParticleParameterEditor(Player player, String monsterId, String particleType) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            // 尝试重新加载配置
            if (logger != null) {
                logger.warning("粒子参数编辑器中找不到怪物配置，尝试重新加载:");
                logger.warning("- 怪物ID: " + monsterId);
                logger.warning("- 可用的怪物ID: " + String.join(", ", idzManager.getAllMonsterIds()));
            }

            // 强制重新加载配置
            idzManager.reloadConfig();
            config = idzManager.getMonsterConfig(monsterId);

            if (config == null) {
                player.sendMessage(ChatColor.RED + "❌ IDZ怪物不存在: " + monsterId);
                player.sendMessage(ChatColor.YELLOW + "请检查怪物是否已被删除或配置文件是否损坏");
                return;
            } else {
                if (logger != null) {
                    logger.info("重新加载后成功找到怪物配置: " + monsterId);
                }
            }
        }

        editingMonster.put(player, monsterId);
        editingParticleType.put(player, particleType);

        String particleDisplayName = getParticleDisplayName(particleType);
        Inventory gui = Bukkit.createInventory(null, 54, "§6粒子参数编辑器 - " + particleDisplayName);

        // 填充背景
        fillBackground(gui);

        // 设置参数编辑按钮
        setupParticleParameterButtons(gui, config, particleType);

        // 设置控制按钮
        setupParticleParameterControlButtons(gui);

        player.openInventory(gui);

        if (logger != null) {
            logger.info("为玩家 " + player.getName() + " 打开粒子参数编辑器: " + particleType);
        }
    }

    /**
     * 设置粒子参数编辑按钮
     */
    private void setupParticleParameterButtons(Inventory gui, IDZMonsterConfig config, String particleType) {
        String particleDisplayName = getParticleDisplayName(particleType);

        // 粒子数量参数 (槽位10)
        ItemStack countButton = createParameterEditButton(
            Material.EMERALD,
            "§a粒子数量",
            config.getParticleCount(),
            "个",
            Arrays.asList(
                "§7控制每次生成的粒子数量",
                "§7数量越多效果越密集",
                "§7范围: 1-100"
            )
        );
        gui.setItem(10, countButton);

        // 粒子范围参数 (槽位11)
        ItemStack rangeButton = createParameterEditButton(
            Material.COMPASS,
            "§b粒子范围",
            config.getParticleRange(),
            "格",
            Arrays.asList(
                "§7控制粒子散布的范围",
                "§7范围越大粒子分布越广",
                "§7范围: 0.5-10.0"
            )
        );
        gui.setItem(11, rangeButton);

        // 粒子间隔参数 (槽位12)
        ItemStack intervalButton = createParameterEditButton(
            Material.CLOCK,
            "§e粒子间隔",
            config.getParticleInterval(),
            "tick",
            Arrays.asList(
                "§7控制粒子生成的时间间隔",
                "§7间隔越小生成越频繁",
                "§7范围: 5-100 (20tick=1秒)"
            )
        );
        gui.setItem(12, intervalButton);

        // 预览时长参数 (槽位13)
        ItemStack durationButton = createParameterEditButton(
            Material.REPEATER,
            "§d预览时长",
            config.getPreviewDuration(),
            "秒",
            Arrays.asList(
                "§7控制预览粒子的持续时间",
                "§7时长越长预览越久",
                "§7范围: 1-30"
            )
        );
        gui.setItem(13, durationButton);
    }

    /**
     * 创建参数编辑按钮
     */
    private ItemStack createParameterEditButton(Material material, String name, Object currentValue, String unit, List<String> description) {
        ItemStack button = new ItemStack(material);
        ItemMeta meta = button.getItemMeta();

        meta.setDisplayName(name);

        List<String> lore = new ArrayList<>(description);
        lore.add("");
        lore.add("§7当前值: §a" + currentValue + " " + unit);
        lore.add("");
        lore.add("§e点击修改此参数");

        meta.setLore(lore);
        button.setItemMeta(meta);
        return button;
    }

    /**
     * 设置粒子参数编辑器的控制按钮
     */
    private void setupParticleParameterControlButtons(Inventory gui) {
        // 返回按钮
        ItemStack backButton = createButton(Material.ARROW,
            "§e← 返回粒子编辑器",
            Arrays.asList("§7返回粒子效果编辑界面"));
        gui.setItem(49, backButton);

        // 高级设置按钮
        ItemStack advancedButton = createButton(Material.COMPARATOR,
            "§d⚙ 高级设置",
            Arrays.asList(
                "§7打开高级粒子参数设置",
                "§7配置速度、偏移、颜色等",
                "§e点击打开高级设置"
            ));
        gui.setItem(50, advancedButton);

        // 预览按钮
        ItemStack previewButton = createButton(Material.ENDER_EYE,
            "§a👁 预览效果",
            Arrays.asList("§7使用当前参数预览粒子效果"));
        gui.setItem(52, previewButton);

        // 保存按钮
        ItemStack saveButton = createButton(Material.EMERALD,
            "§a✓ 保存参数",
            Arrays.asList("§7保存当前粒子参数配置"));
        gui.setItem(53, saveButton);
    }

    /**
     * 处理粒子参数编辑器点击事件
     */
    private void handleParticleParameterEditorClick(Player player, int slot, org.bukkit.event.inventory.ClickType clickType) {
        String monsterId = editingMonster.get(player);
        String particleType = editingParticleType.get(player);

        if (monsterId == null || particleType == null) {
            return;
        }

        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            return;
        }

        if (slot == 10) {
            // 编辑粒子数量
            requestParameterInput(player, "count", "粒子数量", config.getParticleCount(), "个", "1-100");
        } else if (slot == 11) {
            // 编辑粒子范围
            requestParameterInput(player, "range", "粒子范围", config.getParticleRange(), "格", "0.5-10.0");
        } else if (slot == 12) {
            // 编辑粒子间隔
            requestParameterInput(player, "interval", "粒子间隔", config.getParticleInterval(), "tick", "5-100");
        } else if (slot == 13) {
            // 编辑预览时长
            requestParameterInput(player, "duration", "预览时长", config.getPreviewDuration(), "秒", "1-30");
        } else if (slot == 49) {
            // 返回粒子编辑器
            openParticleEditor(player, monsterId, false);
        } else if (slot == 50) {
            // 高级设置
            openAdvancedSettings(player, monsterId);
        } else if (slot == 52) {
            // 预览效果
            previewSingleParticle(player, config, particleType);
        } else if (slot == 53) {
            // 保存参数
            saveParticleParameters(player, monsterId);
        }
    }

    /**
     * 请求参数输入
     */
    private void requestParameterInput(Player player, String paramType, String paramName, Object currentValue, String unit, String range) {
        String monsterId = editingMonster.get(player);

        // 验证怪物ID是否有效
        if (monsterId == null || !idzManager.monsterExists(monsterId)) {
            if (logger != null) {
                logger.warning("请求参数输入时怪物ID无效:");
                logger.warning("- 玩家: " + player.getName());
                logger.warning("- 怪物ID: " + monsterId);
                logger.warning("- 参数类型: " + paramType);
            }
            player.sendMessage(ChatColor.RED + "❌ 怪物配置丢失，请重新打开编辑器");
            return;
        }

        // 正确存储怪物ID而不是玩家名称
        waitingForInput.put(player, monsterId);
        inputType.put(player, paramType);

        player.closeInventory();

        player.sendMessage("");
        player.sendMessage(ChatColor.GOLD + "=== 粒子参数配置 ===");
        player.sendMessage(ChatColor.YELLOW + "请输入新的 " + ChatColor.AQUA + paramName + ChatColor.YELLOW + ":");
        player.sendMessage(ChatColor.GRAY + "当前值: " + ChatColor.WHITE + currentValue + " " + unit);
        player.sendMessage(ChatColor.GRAY + "有效范围: " + ChatColor.WHITE + range);
        player.sendMessage("");
        player.sendMessage(ChatColor.GRAY + "输入 " + ChatColor.RED + "'cancel'" + ChatColor.GRAY + " 取消修改");
        player.sendMessage(ChatColor.GOLD + "==================");
        player.sendMessage("");
    }

    /**
     * 预览单个粒子效果
     */
    private void previewSingleParticle(Player player, IDZMonsterConfig config, String particleType) {
        // 停止之前的预览
        stopParticlePreview(player);

        player.sendMessage(ChatColor.GREEN + "开始预览粒子效果: " + getParticleDisplayName(particleType));

        // 创建临时的单粒子列表
        Set<String> tempSelected = new HashSet<>();
        tempSelected.add(particleType);
        selectedParticles.put(player, tempSelected);

        // 开始预览
        startParticlePreview(player, config);
    }

    /**
     * 保存粒子参数
     */
    private void saveParticleParameters(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "❌ 配置不存在，无法保存");
            return;
        }

        try {
            // 保存配置
            boolean saveResult = idzManager.updateMonsterConfig(monsterId, config);

            if (saveResult) {
                player.sendMessage(ChatColor.GREEN + "✅ 粒子参数已成功保存！");
                player.sendTitle("", ChatColor.GREEN + "✅ 参数保存成功", 10, 30, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.2f);

                if (logger != null) {
                    logger.info("粒子参数保存成功: " + monsterId);
                }
            } else {
                player.sendMessage(ChatColor.RED + "❌ 保存参数失败，请检查控制台错误信息");
                player.sendTitle("", ChatColor.RED + "❌ 保存失败", 10, 30, 10);
                player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);

                if (logger != null) {
                    logger.warning("粒子参数保存失败: " + monsterId);
                }
            }

        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "❌ 保存参数时发生错误: " + e.getMessage());
            player.sendTitle("", ChatColor.RED + "❌ 保存出错", 10, 30, 10);
            player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_VILLAGER_NO, 1.0f, 1.0f);

            if (logger != null) {
                logger.severe("保存粒子参数时发生异常: " + monsterId);
                e.printStackTrace();
            }
        }
    }
}
