package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.Ver_zhzh.customZombie.UserMaker.gui.AttributeEditorGUI;
import org.Ver_zhzh.customZombie.UserMaker.gui.EquipmentEditorGUI;
import org.Ver_zhzh.customZombie.UserMaker.gui.ParticleEditorGUI;
import org.Ver_zhzh.customZombie.UserMaker.gui.SkillSelectorGUI;

import java.util.*;
import java.util.logging.Logger;

/**
 * IDZ怪物GUI管理器
 * 提供可视化的怪物编辑界面
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IDZMonsterGUI implements Listener {
    
    private final Plugin plugin;
    private final Logger logger;
    private final IDZMonsterManager idzManager;

    // 子GUI管理器
    private final AttributeEditorGUI attributeEditor;
    private final EquipmentEditorGUI equipmentEditor;
    private final SkillSelectorGUI skillSelector;
    private final ParticleEditorGUI particleEditor;
    
    // GUI标题常量
    private static final String MAIN_GUI_TITLE = "§6IDZ怪物编辑器";
    private static final String ATTRIBUTE_GUI_TITLE = "§6属性编辑器";
    private static final String EQUIPMENT_GUI_TITLE = "§6装备编辑器";
    private static final String SKILL_GUI_TITLE = "§6技能选择器";
    private static final String PARTICLE_GUI_TITLE = "§6粒子特效编辑器";
    
    // 当前编辑的怪物追踪
    private final Map<Player, String> editingMonsters;
    private final Map<Player, String> currentGUIType;
    
    // GUI槽位常量
    private static final int ATTRIBUTES_SLOT = 10;
    private static final int EQUIPMENT_SLOT = 12;
    private static final int SKILLS_SLOT = 14;
    private static final int PARTICLES_SLOT = 16;
    private static final int PREVIEW_SLOT = 22;
    private static final int SAVE_SLOT = 40;
    private static final int CANCEL_SLOT = 44;
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     * @param idzManager IDZ怪物管理器
     */
    public IDZMonsterGUI(Plugin plugin, IDZMonsterManager idzManager) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.idzManager = idzManager;
        this.editingMonsters = new HashMap<>();
        this.currentGUIType = new HashMap<>();

        // 初始化子GUI管理器
        this.attributeEditor = new AttributeEditorGUI(plugin, idzManager);
        this.equipmentEditor = new EquipmentEditorGUI(plugin, idzManager);
        this.skillSelector = new SkillSelectorGUI(plugin, idzManager);
        this.particleEditor = new ParticleEditorGUI(plugin, idzManager);

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, plugin);

        logger.info("IDZ怪物GUI管理器初始化完成");
    }
    
    /**
     * 打开主编辑界面
     * 
     * @param player 玩家
     * @param monsterId 怪物ID
     */
    public void openMainEditor(Player player, String monsterId) {
        IDZMonsterConfig config = idzManager.getMonsterConfig(monsterId);
        if (config == null) {
            player.sendMessage(ChatColor.RED + "IDZ怪物不存在: " + monsterId);
            return;
        }
        
        // 记录当前编辑状态
        editingMonsters.put(player, monsterId);
        currentGUIType.put(player, "main");
        
        // 创建GUI界面
        Inventory gui = Bukkit.createInventory(null, 54, MAIN_GUI_TITLE + " - " + config.getDisplayName());
        
        // 填充背景
        fillBackground(gui);
        
        // 设置功能按钮
        setupMainGUIButtons(gui, config);
        
        // 显示怪物信息
        setupMonsterInfo(gui, config);
        
        player.openInventory(gui);
        
        logger.info("为玩家 " + player.getName() + " 打开IDZ怪物主编辑界面: " + monsterId);
    }
    
    /**
     * 填充GUI背景
     */
    private void fillBackground(Inventory gui) {
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);
        
        // 填充边框
        for (int i = 0; i < 9; i++) {
            gui.setItem(i, background);
            gui.setItem(i + 45, background);
        }
        for (int i = 0; i < 54; i += 9) {
            gui.setItem(i, background);
            gui.setItem(i + 8, background);
        }
    }
    
    /**
     * 设置主GUI的功能按钮
     */
    private void setupMainGUIButtons(Inventory gui, IDZMonsterConfig config) {
        // 属性编辑按钮
        ItemStack attributesButton = createButton(Material.DIAMOND_SWORD, 
            "§e属性编辑", 
            Arrays.asList(
                "§7点击编辑怪物基础属性",
                "§7生命值: §c" + config.getHealth(),
                "§7移动速度: §a" + config.getMovementSpeed(),
                "§7攻击伤害: §c" + config.getAttackDamage(),
                "§7护甲值: §9" + config.getArmor()
            ));
        gui.setItem(ATTRIBUTES_SLOT, attributesButton);
        
        // 装备编辑按钮
        ItemStack equipmentButton = createButton(Material.IRON_CHESTPLATE,
            "§e装备编辑",
            Arrays.asList(
                "§7点击编辑怪物装备",
                "§7主手、副手、护甲配置",
                "§7自定义武器和防具"
            ));
        gui.setItem(EQUIPMENT_SLOT, equipmentButton);
        
        // 技能选择按钮
        ItemStack skillsButton = createButton(Material.ENCHANTED_BOOK,
            "§e技能选择",
            Arrays.asList(
                "§7点击选择怪物技能",
                "§7当前技能数量: §b" + config.getSkillIds().size(),
                "§7从47种技能中自由组合"
            ));
        gui.setItem(SKILLS_SLOT, skillsButton);
        
        // 粒子特效按钮
        ItemStack particlesButton = createButton(Material.FIREWORK_ROCKET,
            "§e粒子特效",
            Arrays.asList(
                "§7点击编辑粒子特效",
                "§7当前特效: §d" + config.getParticleType(),
                "§7自定义视觉效果"
            ));
        gui.setItem(PARTICLES_SLOT, particlesButton);
        
        // 预览测试按钮
        ItemStack previewButton = createButton(Material.ZOMBIE_SPAWN_EGG,
            "§a预览测试",
            Arrays.asList(
                "§7点击生成测试怪物",
                "§7在你的位置生成怪物",
                "§7用于测试配置效果"
            ));
        gui.setItem(PREVIEW_SLOT, previewButton);
        
        // 保存按钮
        ItemStack saveButton = createButton(Material.EMERALD,
            "§a保存配置",
            Arrays.asList(
                "§7保存所有修改",
                "§7并关闭编辑器"
            ));
        gui.setItem(SAVE_SLOT, saveButton);
        
        // 取消按钮
        ItemStack cancelButton = createButton(Material.BARRIER,
            "§c取消编辑",
            Arrays.asList(
                "§7放弃所有修改",
                "§7并关闭编辑器"
            ));
        gui.setItem(CANCEL_SLOT, cancelButton);
    }
    
    /**
     * 设置怪物信息显示
     */
    private void setupMonsterInfo(Inventory gui, IDZMonsterConfig config) {
        // 怪物头像和基本信息
        ItemStack monsterInfo = createButton(Material.ZOMBIE_HEAD,
            "§6" + config.getDisplayName(),
            Arrays.asList(
                "§7怪物ID: §e" + config.getMonsterId(),
                "§7实体类型: §b" + config.getEntityType().name(),
                "§7描述: §f" + config.getDescription(),
                "",
                "§7=== 基础属性 ===",
                "§c❤ 生命值: " + config.getHealth(),
                "§a⚡ 移动速度: " + config.getMovementSpeed(),
                "§c⚔ 攻击伤害: " + config.getAttackDamage(),
                "§9🛡 护甲值: " + config.getArmor(),
                "",
                "§7=== 技能信息 ===",
                "§b✨ 技能数量: " + config.getSkillIds().size(),
                "§d🎆 粒子特效: " + config.getParticleType(),
                "",
                "§7=== 行为设置 ===",
                "§c⚔ 敌对性: " + (config.isHostile() ? "§a是" : "§c否")
            ));
        gui.setItem(4, monsterInfo);
    }
    
    /**
     * 创建按钮物品
     */
    private ItemStack createButton(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(name);
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }
    
    /**
     * 处理GUI点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();
        
        // 检查是否是IDZ GUI
        if (!isIDZGUI(title)) {
            return;
        }
        
        // 取消事件，防止物品被拿走
        event.setCancelled(true);
        
        int slot = event.getRawSlot();
        String currentGUI = currentGUIType.get(player);
        
        if ("main".equals(currentGUI)) {
            handleMainGUIClick(player, slot);
        }
        // 其他GUI类型的处理将在后续添加
    }
    
    /**
     * 处理主GUI点击
     */
    private void handleMainGUIClick(Player player, int slot) {
        String monsterId = editingMonsters.get(player);
        if (monsterId == null) {
            return;
        }

        switch (slot) {
            case ATTRIBUTES_SLOT:
                openAttributeEditor(player, monsterId);
                break;

            case EQUIPMENT_SLOT:
                openEquipmentEditor(player, monsterId);
                break;

            case SKILLS_SLOT:
                openSkillSelector(player, monsterId);
                break;

            case PARTICLES_SLOT:
                openParticleEditor(player, monsterId);
                break;

            case PREVIEW_SLOT:
                previewMonster(player, monsterId);
                break;

            case SAVE_SLOT:
                saveAndClose(player, monsterId);
                break;

            case CANCEL_SLOT:
                cancelAndClose(player);
                break;
        }
    }
    
    /**
     * 打开属性编辑器
     */
    private void openAttributeEditor(Player player, String monsterId) {
        attributeEditor.openAttributeEditor(player, monsterId);
    }
    
    /**
     * 打开装备编辑器
     */
    private void openEquipmentEditor(Player player, String monsterId) {
        equipmentEditor.openEquipmentEditor(player, monsterId);
    }
    
    /**
     * 打开技能选择器
     */
    private void openSkillSelector(Player player, String monsterId) {
        skillSelector.openSkillSelector(player, monsterId);
    }
    
    /**
     * 打开粒子编辑器
     */
    private void openParticleEditor(Player player, String monsterId) {
        particleEditor.openParticleEditor(player, monsterId);
    }
    
    /**
     * 预览怪物
     */
    private void previewMonster(Player player, String monsterId) {
        player.sendMessage(ChatColor.YELLOW + "预览功能将在怪物生成系统完成后实现");
        // TODO: 实现怪物预览生成
    }
    
    /**
     * 保存并关闭
     */
    private void saveAndClose(Player player, String monsterId) {
        player.closeInventory();
        player.sendMessage(ChatColor.GREEN + "IDZ怪物配置已保存: " + monsterId);
        cleanupPlayerData(player);
    }
    
    /**
     * 取消并关闭
     */
    private void cancelAndClose(Player player) {
        player.closeInventory();
        player.sendMessage(ChatColor.GRAY + "已取消编辑");
        cleanupPlayerData(player);
    }
    
    /**
     * 处理GUI关闭事件
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        String title = event.getView().getTitle();
        
        if (isIDZGUI(title)) {
            // 延迟清理，避免在切换GUI时误清理
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                if (!player.getOpenInventory().getTitle().contains("IDZ")) {
                    cleanupPlayerData(player);
                }
            }, 1L);
        }
    }
    
    /**
     * 检查是否是IDZ GUI
     */
    private boolean isIDZGUI(String title) {
        return title != null && (
            title.startsWith(MAIN_GUI_TITLE) ||
            title.startsWith(ATTRIBUTE_GUI_TITLE) ||
            title.startsWith(EQUIPMENT_GUI_TITLE) ||
            title.startsWith(SKILL_GUI_TITLE)
            // 注意：移除PARTICLE_GUI_TITLE检查，因为ParticleEditorGUI有自己的事件处理器
        );
    }
    
    /**
     * 清理玩家数据
     */
    private void cleanupPlayerData(Player player) {
        editingMonsters.remove(player);
        currentGUIType.remove(player);
    }
    
    /**
     * 关闭管理器，清理资源
     */
    public void shutdown() {
        editingMonsters.clear();
        currentGUIType.clear();

        // 清理子GUI管理器
        for (Player player : Bukkit.getOnlinePlayers()) {
            attributeEditor.cleanupPlayer(player);
            equipmentEditor.cleanupPlayer(player);
            skillSelector.cleanupPlayer(player);
        }

        logger.info("IDZ怪物GUI管理器已关闭");
    }

    /**
     * 获取装备编辑器
     */
    public EquipmentEditorGUI getEquipmentEditor() {
        return equipmentEditor;
    }
}
